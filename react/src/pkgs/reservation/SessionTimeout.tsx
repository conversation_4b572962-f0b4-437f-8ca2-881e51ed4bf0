import React, { useEffect, useRef } from 'react'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogActions from '@mui/material/DialogActions'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import { SESSION_DURATION_MS } from './useEditingSession'

const TIMEOUT_WARNING_MS = 30 * 1000

type SessionTimeoutProps = {
    isActive: boolean | null
    onTimeout: () => void
}

export function SessionTimeout({ isActive, onTimeout }: SessionTimeoutProps) {
    const timeoutRef = useRef<ReturnType<typeof setTimeout>>()
    const countdownRef = useRef<ReturnType<typeof setInterval>>()

    const [open, setOpen] = React.useState(false)
    const [countdown, setCountdown] = React.useState(0)

    const clearAllTimers = () => {
        clearTimeout(timeoutRef.current)
        clearInterval(countdownRef.current)
    }

    const reset = () => {
        clearAllTimers()
        setOpen(false)
        setCountdown(0)

        if (!isActive) {
            return
        }

        // Start the session timeout - show warning dialog before session expires
        timeoutRef.current = setTimeout(() => {
            setOpen(true)
            setCountdown(Math.floor(TIMEOUT_WARNING_MS / 1000))

            // Start countdown timer
            countdownRef.current = setInterval(() => {
                setCountdown((prev) => {
                    if (prev <= 1) {
                        // Time's up - trigger timeout
                        clearAllTimers()
                        setOpen(false)
                        setTimeout(onTimeout, 1)
                        return 0
                    }
                    return prev - 1
                })
            }, 1000)
        }, SESSION_DURATION_MS - TIMEOUT_WARNING_MS)
    }

    useEffect(() => {
        if (isActive === null) return

        reset()

        return () => {
            clearAllTimers()
        }
    }, [isActive])

    return open ? (
        <Dialog
            open={!!isActive && open}
            keepMounted
            aria-labelledby='alert-dialog-slide-title'
            aria-describedby='alert-dialog-slide-description'
        >
            <DialogTitle>Still editing?</DialogTitle>
            <DialogContent>
                <DialogContentText>
                    Your editing session will expire in {countdown} seconds, do you want to extend it?
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button
                    onClick={() => {
                        onTimeout()
                        setOpen(false)
                    }}
                    color='primary'
                >
                    End Session
                </Button>
                <Button
                    color='primary'
                    onClick={() => {
                        clearAllTimers()
                        setOpen(false)
                        setCountdown(0)
                        // Restart the session timeout cycle
                        reset()
                    }}
                    data-testid='dialog-agree'
                >
                    Extend
                </Button>
            </DialogActions>
        </Dialog>
    ) : null
}
