import { Box, Stack, Switch, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { primaryTheme } from '@/app/theme'
import { useReservable } from '@/pkgs/reservation/useReservable'
import { SessionTimeout } from '@/pkgs/reservation/SessionTimeout'
import { ReservationInfo } from '@/pkgs/reservation/ReservationInfo'

interface ReservationSwitchProps {
    ID: string
    Workspace: string
    table: 'content' /* | future support */
    onChange?: (checked: boolean) => void
    disabled?: boolean
    hideExtendedLock?: boolean
}

export function ReservationSwitch({
    ID,
    Workspace,
    table,
    disabled,
    onChange,
    hideExtendedLock = false
}: ReservationSwitchProps) {
    const key = `${table}::${ID}::${Workspace}`
    const { loading, value, start, end, reservableInfo } = useReservable(key)

    useEffect(() => {
        onChange?.(value ?? false)
    }, [value])

    return (
        <>
            <Box>
                <Stack direction='row' component='label' alignItems='center' justifyContent='center'>
                    <Typography>Read</Typography>
                    <Switch
                        checked={value ?? false}
                        onChange={(e, checked) => (checked ? start() : end())}
                        style={{
                            color: value ? primaryTheme.palette.primary.main : primaryTheme.palette.warning.main
                        }}
                        disabled={loading}
                    />
                    <Typography>Edit</Typography>
                    {!hideExtendedLock && (
                        <ReservationInfo reservableInfo={reservableInfo} reservationKey={key} isActive={value} />
                    )}
                </Stack>
            </Box>

            <SessionTimeout
                isActive={value}
                onTimeout={() => {
                    end()
                }}
            />
        </>
    )
}
