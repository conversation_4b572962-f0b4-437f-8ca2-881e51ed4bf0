import { string, z } from 'zod'
import { useAppContext } from '@/pkgs/auth/atoms'
import { useQuery } from '@tanstack/react-query'
import { httpPost } from '@/common/client'
import { useIDToName } from '@/pkgs/grid/cells/GridCells'
import { useEffect } from 'react'
import { RESERVABLE_API } from '@/common/constants'

const reservable = z.object({
    Key: string(),
    CurrentEditor: string().nullish(),
    EditingSession: z.coerce.date().nullish(),
    ExtendedLock: z.coerce.date().nullish()
})

export type Reservable = z.infer<typeof reservable>
export type ReservableInfo = ReturnType<typeof useReservableInfo>

export function useReservableInfo({ reservationKey, isActive }: { reservationKey: string; isActive: boolean | null }) {
    const appContext = useAppContext()
    const result = useQuery({
        queryKey: ['reservation-info', reservationKey],
        enabled: Boolean(reservationKey) && isActive !== null,
        keepPreviousData: true,
        queryFn: async () => httpPost(`${RESERVABLE_API}`, { ReservationKey: reservationKey }, reservable)
    })
    const name = useIDToName({
        tableName: 'account',
        ID: result.data?.CurrentEditor
    })

    const editingSessionEnds = result.data?.EditingSession?.toLocaleString()
    const extendedLockEnds = result.data?.ExtendedLock?.toLocaleString()
    const displayName = name || result.data?.CurrentEditor
    const isMine = result.data?.CurrentEditor === appContext?.identity()?.ID
    let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'info'
    let message = ''
    let locked = Boolean(extendedLockEnds)

    if (isActive) {
        if (extendedLockEnds) {
            color = 'info'
            message = `You have an extended lock on this item until ${extendedLockEnds}.`
        } else {
            color = 'success'
            message = `You are currently editing this item.`
        }
    } else if (Boolean(result.data?.CurrentEditor)) {
        if (isMine) {
            color = 'info'
            message = `You can edit this item.`

            if (editingSessionEnds) {
                color = 'warning'
                message = `You are currently editing this item in another tab. Session ends at ${editingSessionEnds}.`
            }
            if (extendedLockEnds) {
                message += `You have an extended lock on this item until ${extendedLockEnds}.`
            }
        } else {
            if (extendedLockEnds) {
                color = 'warning'
                message = `This item is currently locked by ${displayName} until ${extendedLockEnds}.`
            } else if (editingSessionEnds) {
                color = 'warning'
                message = `This item is currently being edited by ${displayName} until ${editingSessionEnds}.`
            }
        }
    } else {
        color = 'info'
        message = `You can edit this item.`
        locked = false
    }

    return {
        result,
        color,
        message,
        locked,
        isMine
    }
}
