import { ReservableInfo } from '@/pkgs/reservation/useReservableInfo'
import CMDialog from '@/common/components/CMDialog'
import DialogContent from '@mui/material/DialogContent'
import { Alert, Button, FormControlLabel, Stack, Switch } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { useAppContext } from '@/pkgs/auth/atoms'
import { RESERVABLE_API } from '@/common/constants'
import { httpPost } from '@/common/client'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import moment from 'moment'

export function LockingDialog(props: {
    open: boolean
    onClose: () => void
    reservationKey: string
    isActive: boolean
    reservableInfo: ReservableInfo
}) {
    const appContext = useAppContext()
    const { open, onClose, reservationKey, isActive, reservableInfo } = props
    const reservable = reservableInfo.result.data

    const [isLoading, setIsLoading] = useState(reservableInfo.result.isLoading)
    const [hasExtendedLock, setHasExtendedLock] = useState(Boolean(reservable?.ExtendedLock))
    const [extendedLockEnds, setExtendedLockEnds] = useState(
        reservable?.ExtendedLock ? moment(reservable.ExtendedLock) : null
    )

    useEffect(() => {
        if (!open) return
        reservableInfo.result.refetch()
    }, [open])

    const saveExtendedLock = async () => {
        setIsLoading(true)
        try {
            const lockUntil = hasExtendedLock && extendedLockEnds ? extendedLockEnds.toISOString() : null
            await httpPost(`${RESERVABLE_API}/lock/set`, {
                ReservationKey: reservationKey,
                ExtendedLock: lockUntil
            })
            await reservableInfo.result.refetch()
            onClose()
        } catch (error) {
            notify(guessErrorMessage(error), 'error')
        } finally {
            setIsLoading(false)
        }
    }

    const forceClearLock = async () => {
        setIsLoading(true)
        try {
            await httpPost(`${RESERVABLE_API}/lock/force-clear`, { ReservationKey: reservationKey })
            await reservableInfo.result.refetch()
            setHasExtendedLock(false)
            setExtendedLockEnds(null)
            onClose()
        } catch (error) {
            notify(guessErrorMessage(error), 'error')
        } finally {
            setIsLoading(false)
        }
    }

    if (!open || !reservable) return null
    return (
        <CMDialog open={open} onClose={onClose} title={'Reservation details'} showCloseButton>
            <DialogContent>
                <Stack direction={'column'} spacing={2}>
                    <Alert severity={reservableInfo.color}>{reservableInfo.message}</Alert>
                    {!isActive && (
                        <Alert severity={'info'}>You need an active editing session to change the lock.</Alert>
                    )}
                    <Stack direction={'row'} spacing={2}>
                        <FormControlLabel
                            control={<Switch />}
                            label={'Extended lock'}
                            labelPlacement={'start'}
                            checked={hasExtendedLock}
                            onChange={(e) => {
                                if (hasExtendedLock) {
                                    setExtendedLockEnds(null)
                                } else {
                                    setExtendedLockEnds(moment().add(1, 'hour'))
                                }
                                setHasExtendedLock(!hasExtendedLock)
                            }}
                            disabled={!isActive || isLoading}
                        />
                        <DateTimePicker
                            value={extendedLockEnds}
                            onChange={(v) => setExtendedLockEnds(v)}
                            disabled={!isActive || isLoading || !hasExtendedLock}
                            minDateTime={moment()}
                        />
                    </Stack>

                    <Stack direction={'row'} spacing={2} justifyContent={'flex-end'}>
                        {appContext?.identity()?.IsAdmin && reservable?.ExtendedLock && (
                            <Button
                                variant={'contained'}
                                color={'error'}
                                onClick={() => forceClearLock()}
                                size={'small'}
                                disabled={isLoading}
                            >
                                Force clear lock
                            </Button>
                        )}
                        <Button variant={'contained'} onClick={saveExtendedLock} disabled={!isActive || isLoading}>
                            Save
                        </Button>
                    </Stack>
                </Stack>
            </DialogContent>
        </CMDialog>
    )
}
