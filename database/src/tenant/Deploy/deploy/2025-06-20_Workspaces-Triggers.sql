-- Deploy cm_tenant_db_DeploySchema:2025-06-20_Workspaces-Triggers to pg

BEGIN;

----------------
-- create materialized view for active workspaces
----------------

-- 0) create the materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_active_workspaces AS
SELECT array_agg(id) AS workspace_list
FROM public.workspaces
WHERE active = true;

CREATE UNIQUE INDEX ON mv_active_workspaces((1));

GRANT ALL ON mv_active_workspaces TO contentmanager_application_user;

-- 1) create (or replace) the trigger function
CREATE OR REPLACE FUNCTION public.refresh_mv_active_workspaces()
    RETURNS trigger
    LANGUAGE plpgsql
AS $$
BEGIN
    -- this will rebuild the view to include only active workspaces
    REFRESH MATERIALIZED VIEW mv_active_workspaces;
    RETURN NULL;
END;
$$;

-- 2) drop any existing trigger to avoid duplicates
DROP TRIGGER IF EXISTS trg_refresh_mv_active_workspaces
    ON public.workspaces;

-- 3) attach the trigger to workspaces for INSERT/UPDATE/DELETE
CREATE TRIGGER trg_refresh_mv_active_workspaces
    AFTER INSERT OR UPDATE OR DELETE
    ON public.workspaces
    FOR EACH STATEMENT
EXECUTE FUNCTION public.refresh_mv_active_workspaces();

GRANT EXECUTE ON FUNCTION public.refresh_mv_active_workspaces() TO contentmanager_application_user;

-- 4) refresh the view for the first time
REFRESH MATERIALIZED VIEW mv_active_workspaces;

----------------
-- update effective_in for the content
----------------
-- 1. Create (or replace) the trigger function
CREATE OR REPLACE FUNCTION public.content_update_effective_in()
    RETURNS trigger
    LANGUAGE plpgsql
AS $$
DECLARE
    target_id    uuid;
    v_should_process boolean := false;
    new_effective varchar[];
BEGIN
--     v_should_process :=
--             (TG_OP = 'INSERT')
--                 OR (TG_OP = 'DELETE' AND OLD.workspace       <> 'live')
--                 OR (TG_OP = 'UPDATE' AND OLD.workspace IS DISTINCT FROM NEW.workspace);
--
--     IF NOT v_should_process THEN
--         RETURN NULL;   -- skip the rest of the logic
--     END IF;

    -- Determine which content ID to operate on:
    IF TG_OP = 'DELETE' THEN
        target_id := OLD.id;
    ELSE
        target_id := NEW.id;
    END IF;

    -- Pull the one row of workspace_list out of the materialized view
    -- then unnest it, filter out 'live', and drop any that already have
    -- a content row for this id:
    SELECT COALESCE(array_agg(ws), ARRAY[]::varchar[])
    INTO   new_effective
    FROM   unnest(
                   (SELECT workspace_list
                    FROM mv_active_workspaces)
           ) AS ws
    WHERE  ws <> 'live'
      AND  NOT EXISTS (
        SELECT 1
        FROM   public.content c
        WHERE  c.id        = target_id
          AND  c.workspace = ws
    );

    -- Ensure it's never NULL
    new_effective := COALESCE(new_effective, ARRAY[]::varchar[]);

    -- Update the 'live' row if it exists
    UPDATE public.content
    SET    effective_in = new_effective
    WHERE  id = target_id
      AND  workspace = 'live';

    UPDATE public.content
    SET    effective_in = ARRAY[]::varchar[]
    WHERE  id = target_id
      AND  workspace <> 'live';

    RETURN NULL;  -- return value ignored in AFTER triggers
END;
$$;

-- 2. Attach it as an AFTER trigger on INSERT, DELETE, or whenever 'workspace' changes
DROP TRIGGER IF EXISTS tr_content_update_effective_in
    ON public.content;

CREATE TRIGGER tr_content_update_effective_in
    AFTER INSERT OR DELETE OR UPDATE OF workspace
    ON public.content
    FOR EACH ROW
EXECUTE FUNCTION public.content_update_effective_in();

GRANT EXECUTE ON FUNCTION public.content_update_effective_in() TO contentmanager_application_user;


----------------
-- Modify content table
----------------

--- add index for content workspace
CREATE INDEX IF NOT EXISTS idx_content_id_workspace
    ON public.content(id, workspace);

-- add a check constraint to ensure non-'live' workspaces are always active
ALTER TABLE public.content
    ADD CONSTRAINT chk_non_live_must_be_active
        CHECK (
            workspace = 'live'
                OR active = true
            );

COMMIT;
