# Workspaces

#### Resolve TODO: `TODO: @Anatoly`

## TBD
- [?] Templates' paths. Our `path` approach on each row is not very efficient. Should be refactored. 

## Overview
Users can manage content across multiple workspace environments (similar to git branches),
so that they can prepare and review content changes before publishing them to the public-facing site.

1. For version 1, we will only support two workspaces: `live` and `draft`.
2. "Deletion" works differently for `live` and non-`live` workspaces:
   - `live`: always soft-deleted. When `live` is deleted, other workspaces are not affected.
   - non-`live`: deleted completely.
3. Only `live` can be branched from.

## Implementation details

1. Content entities will implement a Workspaced interface with:

    ```go
   package shared

   type (
           Workspaced interface {
           GetWorkspace() string
           GetEntityID() uuid.UUID
		   SetWorkspace(workspace string)
	    }
   
    )
   ```

   - `EffectiveIn` is an array of strings on `"live"` entities only. 
   - The `EffectiveIn` field will be used for query optimization (as `workspace = ? OR ? = ANY(effective_in)`). 
   - If the entity is not present in the workspace, the system will fall back to the `live` version.
   - The `EffectiveIn` field will be updated when the entity is deleted or created in a specific workspace. Currently this is done via DB trigger.
   - Each time a new workspace is created, the `EffectiveIn` field will be updated to include the new workspace.
