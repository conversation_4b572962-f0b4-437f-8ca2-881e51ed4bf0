package handlers

import (
	shared2 "contentmanager/pkgs/workspaces/shared"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type WorkspacedEntity interface {
	shared2.Workspaced
	schema.Tabler
}

// PublishWorkspacedEntity persists changes to DB. It copies any non-live entity to live workspace.
// The source entity is deleted from DB, entity is mutated to `live`
// The function doesn't handle `Tracking`
func PublishWorkspacedEntity[T WorkspacedEntity](tx *gorm.DB, entity T) error {
	if entity.GetWorkspace() == "live" {
		return fmt.Errorf("Cannot publish live entity. ")
	}

	workspaceToPublish := entity.GetWorkspace()

	return tx.Transaction(func(tx *gorm.DB) error {
		entity.SetWorkspace("live")
		if err := tx.Omit("effective_in").Save(entity).Error; err != nil {
			return err
		}

		// Delete entity from workspace
		if err := tx.Table(entity.TableName()).Where("id = ? AND workspace = ?", entity.GetEntityID(), workspaceToPublish).Delete(nil).Error; err != nil {
			return err
		}

		return nil
	})
}
