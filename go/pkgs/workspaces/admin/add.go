package admin

import (
	middlewares2 "contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/workspaces/handlers"
	"net/http"
)

func AddWorkspaces(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v1/workspaces", func(router httpService.Router) {

		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
		}) {
			utils.WriteResultJSON(w, handlers.GetAllWorkspaces(r))
		})

		router.Get("/:tableName/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath handlers.WorkspacedEntityID
		}) {
			utils.WriteResultJSON(w, handlers.ListEntityWorkspaces(r, params.FromPath))
		})

	}, middlewares2.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware())
	return r
}
