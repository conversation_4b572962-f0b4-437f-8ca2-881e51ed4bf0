package admin

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/structure"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"time"
)

var _ commonModels.IContent = (*BaseContent)(nil)

type (
	AccountName struct {
		ID        uuid.UUID
		Firstname string
		Lastname  string
	}
	BaseContent struct {
		ID           uuid.UUID                `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
		Workspace    string                   `gorm:"primaryKey;type:text"`
		EffectiveIn  dbDriver.PgStringArray   `gorm:"type:text[]"`
		Type         commonModels.ContentType `json:"type" gorm:"column:type;type:content_type"`
		Owner        uuid.UUID                `json:"owner" gorm:"column:owner;type:uuid" `
		Publisher    uuid.UUID                `json:"publisher" gorm:"column:publisher;type:uuid"`
		Title        string                   `json:"title" gorm:"column:title;type:character varying(256)"`
		Content      string                   `json:"content" gorm:"column:content;type:text"`
		Data         json.RawMessage          `json:"data" gorm:"column:data;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		Structure    json.RawMessage          `json:"structure" gorm:"column:structure;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		Route        string                   `json:"route" gorm:"column:route;type:character varying(256)"`
		Path         string                   `json:"path" gorm:"column:path;type:ltree"`
		PageLayout   commonModels.PageType    `json:"pagelayout" gorm:"column:pagelayout;type:page_style;DEFAULT:'HTML'::page_style;NOT NULL"`
		Created      time.Time                `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
		Updated      time.Time                `json:"updated" gorm:"column:updated;type:timestamp with time zone"`
		Deleted      time.Time                `json:"deleted" gorm:"column:deleted;type:timestamp with time zone"`
		PrivacyLevel int                      `json:"privacyLevel" gorm:"column:privacy_level;type:integer"`
		Approved     bool                     `json:"approved" gorm:"column:approved;type:boolean;DEFAULT:true"`
		Active       bool                     `json:"active" gorm:"column:active;type:boolean;DEFAULT:true"`
		Sites        dbDriver.PgUUIDArray     `json:"sites" gorm:"column:sites;type:uuid[]"`
		MediaID      uuid.NullUUID            `json:"mediaId" gorm:"column:media_id;type:uuid"`
		Settings     json.RawMessage          `json:"settings" gorm:"column:settings;type:jsonb;NOT NULL;DEFAULT '{}'::jsonb"`
		DepartmentId uuid.NullUUID            `json:"departmentId" gorm:"column:department_id;type:uuid; DEFAULT: NULL"`
		StructureID  *uuid.UUID               `json:"structureId" gorm:"column:structure_id;type:uuid; DEFAULT: NULL"`
		Meta         json.RawMessage          `json:"meta" gorm:"column:meta;type:jsonb`
		PublishAt    *time.Time               `json:"publish_at" gorm:"column:publish_at;type:timestamp with time zone"`
		ExpireAt     *time.Time               `json:"expire_at" gorm:"column:expire_at;type:timestamp with time zone"`
	}
	ContentForManager struct {
		BaseContent
		Media            commonModels.Media     `json:"media" gorm:"foreignKey:media_id"`
		Department       tenancyModels.BaseSite `json:"department"`
		ContentStructure structure.Structure    `gorm:"foreignKey:structure_id"`
		Structures       dbDriver.PgUUIDArray   `json:"structures" gorm:"column:structures;type:uuid[]"`
		OwnerName        AccountName            `json:"ownerName" gorm:"-"`
		PublisherName    AccountName            `json:"publisherName" gorm:"-"`
		NavPath          string                 `json:"navPath" gorm:"column:nav_path;type:ltree"`
		TemplateTitle    string                 `json:"templateTitle" gorm:"column:template_title;type:character varying(256)"`
		NavParents       []ContentNavParent     `json:"navParents" gorm:"-"`
		Status           string                 `json:"status" gorm:"-"`

		Tags   []commonModels.Tag   `json:"tags" gorm:"-"`
		TagIds dbDriver.PgUUIDArray `gorm:"column:tags;type:uuid[]"`
	}

	ContentNavParent struct {
		ID    uuid.UUID `json:"id"`
		Title string    `json:"title"`
		Route string    `json:"route"`
	}

	ContentChain []commonModels.Content
)

func (b BaseContent) GetWorkspace() string {
	return b.Workspace
}

func (b BaseContent) GetEntityID() uuid.UUID {
	return b.ID
}

func (b BaseContent) GetSites() []uuid.UUID {
	if b.Sites == nil {
		return []uuid.UUID{}
	}
	return b.Sites
}

func (b BaseContent) GetDepartmentID() *uuid.UUID {
	if b.DepartmentId.Valid {
		return &b.DepartmentId.UUID
	}
	return nil
}

func (b BaseContent) GetType() string {
	return string(b.Type)
}

func (b BaseContent) GetScopeEntity() string {
	switch b.Type {
	case commonModels.Template, commonModels.CSS, commonModels.JS:
		return "cm.resource." + string(b.Type)
	case commonModels.Page, commonModels.DistributedPage, commonModels.ExternalLinkContentType:
		return "cm.content.page"
	default:
		return "cm.content." + string(b.Type)
	}
}
func (b BaseContent) GetRoute() string {
	return b.Route
}

func (b BaseContent) GetPublished() bool {
	return b.PublishAt != nil
}

func (b BaseContent) GetID() uuid.UUID {
	return b.ID
}

func (ContentForManager) TableName() string {
	return "content"
}
