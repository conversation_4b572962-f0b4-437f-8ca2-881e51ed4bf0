package admin

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/reservation/models"
	"errors"
	"gorm.io/gorm"
	"time"
)

// StartEditingSessionFor starts an editing session for the current user. Extended locks are not affected.
func StartEditingSessionFor(r *shared.AppContext, key string) result.EmptyResult {
	currentSession, sessErr := getValidSession(r)
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}

	return result.CheckEmpty(r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		var reservation models.Reservation
		if err := tx.Where("key = ?", key).First(&reservation).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				reservation = models.Reservation{
					Key:           key,
					CurrentEditor: &r.Account().ID,
				}
			} else {
				return err
			}
		} else if reservation.CurrentEditor != nil && *reservation.CurrentEditor == r.Account().ID &&
			reservation.EditingSession != nil && reservation.EditingSession.Equal(currentSession.UTC()) {
			return nil // Already editing this reservable
		}

		if err := reservation.AvailableFor(r.Account().ID, currentSession); err != nil {
			return err
		}

		if err := evaluatePermissions(r, key); err != nil {
			return err
		}

		reservation.CurrentEditor = &r.Account().ID
		reservation.EditingSession = currentSession

		if err := tx.Save(&reservation).Error; err != nil {
			return err
		}

		return nil
	}))
}

// EndEditingSessionFor ends the current editing session for the current user. Requires only non-null current session.
// Extended locks are not affected.
func EndEditingSessionFor(r *shared.AppContext, key string) result.EmptyResult {
	currentSession, sessErr := r.EditingSession()
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}
	if currentSession == nil {
		return result.ErrorEmpty(errors.New("no editing session found"))
	}

	if err := r.TenantDatabase().Model(&models.Reservation{}).Where("key = ? AND current_editor = ? AND editing_session = ?", key, r.Account().ID, currentSession).Updates(map[string]interface{}{
		"editing_session": nil,
	}).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	return result.SuccessEmpty()
}

// ExtendEditingSession extends the editing session for the current user. Requires a valid new session.
// If the current session is not valid, it does nothing and returns success.
func ExtendEditingSession(r *shared.AppContext, newSession time.Time) result.EmptyResult {
	currentSession, sessErr := r.EditingSession()
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}

	// if current session is invalid, we cannot extend it
	if err := validateFutureTimestamp(currentSession); err != nil {
		return result.SuccessEmpty()
	}

	if err := validateFutureTimestamp(&newSession); err != nil {
		return result.ErrorEmpty(err)
	}

	if err := r.TenantDatabase().Model(&models.Reservation{}).Where("current_editor = ? AND editing_session = ?", r.Account().ID, currentSession).Updates(map[string]interface{}{
		"editing_session": newSession,
	}).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}
