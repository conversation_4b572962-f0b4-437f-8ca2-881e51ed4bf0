#!/bin/bash

# Set the environment variables if not already set
export PGHOST=${PGHOST:-postgres}
export PGUSER=${PGUSER}
export PGPASSWORD=${PGPASSWORD}

echo "Deploying to cm_multitenancy"
sqitch --chdir "./database/src/multitenancy/Deploy" deploy --verify --target "db:pg://${PGUSER}:${PGPASSWORD}@postgres/cm_multitenancy"

# Query the cm_multitenancy database to get the server column values from the tenant table
SERVERS=$(psql -t -A -d cm_multitenancy -c "SELECT server FROM tenant WHERE active;")

# Check for psql command success
if [ $? -ne 0 ]; then
    echo "Failed to fetch server list from cm_multitenancy."
    exit 1
fi

# Loop through each server and deploy using sqitch
for server in $SERVERS; do

    if [[ $server != cm_* ]]; then
        echo "Skipping database $server, as it does not match the required prefix."
        continue
    fi

#    echo "Run custom query..."
#    psql -t -A -h "$PGHOST" -d "$server" -c "GRANT ALL ON TABLE search_data to contentmanager_application_user;"

    echo "Reverting the last migration database: $server"
    sqitch --chdir "./database/src/tenant/Deploy" -v  revert -y --target "db:pg://${PGUSER}:${PGPASSWORD}@postgres/${server}" @HEAD^1

    echo "Deploying to database: $server"
    sqitch --chdir "./database/src/tenant/Deploy" deploy --verify --target "db:pg://${PGUSER}:${PGPASSWORD}@postgres/${server}"

#    echo "Rebase for seed data"
#    sqitch --chdir "./database/src/tenant/Rebase" revert --log-only -y --target "db:pg://${PGUSER}:${PGPASSWORD}@postgres/${server}"
#    sqitch --chdir "./database/src/tenant/Rebase" deploy --verify --target "db:pg://${PGUSER}:${PGPASSWORD}@postgres/${server}"
    if [ $? -ne 0 ]; then
        echo "Deployment failed for database: $server"
        # Optional: stop deploying to other servers upon failure
        # break
    else
        echo "Deployment successful for database: $server"
    fi
done
